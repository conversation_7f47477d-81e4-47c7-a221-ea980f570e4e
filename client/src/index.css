@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS Variables for themes */
:root {
  /* Light theme - Red and white */
  --bg-primary: #ffffff;
  --bg-secondary: #fef2f2;
  --bg-tertiary: #fee2e2;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --accent-primary: #dc2626;
  --accent-secondary: #ef4444;
  --accent-hover: #b91c1c;
  --border-primary: #e5e7eb;
  --border-secondary: #fecaca;
  --shadow-light: rgba(220, 38, 38, 0.1);
  --shadow-medium: rgba(220, 38, 38, 0.15);
}

.dark {
  /* Dark theme - Purple shades */
  --bg-primary: #0f0b1a;
  --bg-secondary: #1a1625;
  --bg-tertiary: #252030;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --accent-primary: #8b5cf6;
  --accent-secondary: #a78bfa;
  --accent-hover: #7c3aed;
  --border-primary: #374151;
  --border-secondary: #6366f1;
  --shadow-light: rgba(139, 92, 246, 0.2);
  --shadow-medium: rgba(139, 92, 246, 0.3);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

/* Enhanced Button Styles with Animations */
.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px var(--shadow-light);
  transform: translateY(0);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-primary));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px var(--shadow-light);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 12px;
  border: 2px solid var(--border-primary);
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px var(--shadow-light);
}

/* Enhanced Card Styles */
.card {
  background-color: var(--bg-primary);
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--shadow-light);
  border: 1px solid var(--border-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: fit-content;
  max-width: 100%;
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px var(--shadow-medium);
  border-color: var(--accent-primary);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--accent-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.card:hover::before {
  transform: scaleX(1);
}

/* Enhanced Input Field Styles */
.input-field {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid var(--border-primary);
  border-radius: 12px;
  outline: none;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.input-field:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 4px var(--shadow-light);
  transform: translateY(-1px);
}

.input-field::placeholder {
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.input-field:focus::placeholder {
  color: transparent;
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced Animations */
@keyframes breathe {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes pulse-glow {

  0%,
  100% {
    box-shadow: 0 0 20px var(--shadow-light);
  }

  50% {
    box-shadow: 0 0 30px var(--shadow-medium);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation Classes */
.breathe {
  animation: breathe 3s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* Enhanced Watchlist Button */
.watchlist-btn {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
}

.watchlist-btn:hover {
  transform: scale(1.1);
}

.watchlist-btn:active {
  transform: scale(0.95);
}

.watchlist-btn.liked {
  animation: breathe 2s ease-in-out infinite;
}

/* Enhanced Star Rating */
.star-rating {
  display: flex;
  gap: 4px;
  align-items: center;
}

.star-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
}

.star-button:hover {
  transform: scale(1.2);
}

.star-button:active {
  transform: scale(0.9);
}

.star-button.filled {
  animation: pulse-glow 1s ease-out;
}

/* Enhanced Interactive Elements */
.interactive-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px var(--shadow-light);
}

/* Loading Shimmer Effect */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 0%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Floating Animation */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Bounce In Animation */
.bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Slide Up Animation */
.slide-up {
  animation: slide-up 0.5s ease-out;
}

/* Enhanced Button Hover Effects */
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
  animation: glow 2s infinite;
}

.btn-primary:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

/* Theme-based Glow Effects */
.glow-primary {
  box-shadow: 0 0 20px var(--accent-primary);
  animation: glow 2s ease-in-out infinite;
}

.glow-secondary {
  box-shadow: 0 0 15px var(--accent-secondary);
}

/* Enhanced Movie Card Animations */
.movie-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
}

.movie-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

.movie-card:hover .movie-poster {
  transform: scale(1.05);
}

.movie-poster {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive Grid Improvements */
.movies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  align-items: start;
}

@media (max-width: 768px) {
  .movies-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .movies-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
}

/* Category Grid Responsive Design - 5 movies per row for full screen */
.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  max-width: 100%;
}

@media (max-width: 1200px) {
  .category-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 12px;
  }
}

/* Category Page Grid - Infinite scrolling grid for category pages */
.category-page-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  max-width: 100%;
}

@media (max-width: 1200px) {
  .category-page-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .category-page-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .category-page-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

/* Search Bar Animations */
.search-suggestion {
  transition: all 0.2s ease;
  transform: translateX(0);
}

.search-suggestion:hover {
  transform: translateX(4px);
  background-color: var(--bg-tertiary);
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Success/Error Message Animations */
.success-message {
  animation: bounce-in 0.5s ease-out;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.error-message {
  animation: bounce-in 0.5s ease-out;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

/* Hero Section Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes breathing {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile Navigation Responsive Styles */
@media (max-width: 768px) {
  /* Show mobile controls on small screens */
  .mobile-controls {
    display: flex !important;
  }

  /* Hide desktop navigation on small screens */
  .desktop-nav {
    display: none !important;
  }

  /* Hide desktop controls on small screens */
  .desktop-controls {
    display: none !important;
  }

  /* Ensure mobile search is properly visible */
  .mobile-controls .search-container {
    position: relative;
    z-index: 60;
  }

  /* Mobile search expanded state */
  .mobile-controls .search-expanded {
    position: fixed;
    top: 72px;
    left: 16px;
    right: 16px;
    z-index: 55;
    background: var(--bg-primary);
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: 0 8px 32px var(--shadow-medium);
    padding: 8px;
  }

  /* Mobile search input styling */
  .mobile-controls input[type="text"] {
    width: 100%;
    min-width: 200px;
    font-size: 16px;
    padding: 12px 16px;
    border: 2px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  /* Mobile search input focus */
  .mobile-controls input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px var(--shadow-light);
  }

  /* Mobile search suggestions */
  .mobile-controls .search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 2px solid var(--border-primary);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 70;
  }

  /* Mobile logo adjustments */
  header a[href="/"] {
    font-size: 24px !important;
  }

  header a[href="/"] img {
    width: 32px !important;
    height: 32px !important;
  }

  /* Mobile header height adjustment */
  header > div > div {
    height: 64px !important;
  }

  /* Ensure proper spacing for mobile controls */
  .mobile-controls {
    gap: 8px !important;
  }

  /* Mobile hamburger button adjustments */
  .hamburger-button {
    min-width: 40px;
    min-height: 40px;
  }
}

@media (min-width: 769px) {
  /* Hide mobile controls on larger screens */
  .mobile-controls {
    display: none !important;
  }

  /* Show desktop navigation on larger screens */
  .desktop-nav {
    display: flex !important;
  }

  /* Show desktop controls on larger screens */
  .desktop-controls {
    display: flex !important;
  }

  /* Hide mobile menu on larger screens */
  .mobile-menu {
    display: none !important;
  }

  .mobile-menu-overlay {
    display: none !important;
  }
}

/* Mobile Menu Animations */
.mobile-menu {
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Hamburger Menu Animation */
.hamburger-button div {
  transform-origin: center;
}

/* Mobile Menu Scrollbar Styling */
.mobile-menu::-webkit-scrollbar {
  width: 6px;
}

.mobile-menu::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.mobile-menu::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 3px;
}

.mobile-menu::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Mobile Navigation Button Animations */
.mobile-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.mobile-nav-button:hover::before {
  left: 100%;
}

.mobile-nav-button:active {
  animation: buttonPulse 0.3s ease-out;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Category Component Styles */
.category-component {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: 20px;
  padding: 24px;
  margin: 20px 0;
  border: 2px solid var(--border-primary);
  box-shadow: 0 8px 32px var(--shadow-light);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.category-component::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

.category-component:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px var(--shadow-medium);
  border-color: var(--accent-primary);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border-radius: 16px;
  border: 1px solid var(--border-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-header:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.category-flag {
  font-size: 32px;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
  animation: flagWave 2s ease-in-out infinite;
}

.category-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px var(--shadow-light);
}

.category-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  margin-top: 4px;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes flagWave {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(-5deg) scale(1.05);
  }
  75% {
    transform: rotate(5deg) scale(1.05);
  }
}

/* Specific Category Styles */
.category-bollywood {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  border: 2px solid #ff6b9d;
}

.category-bollywood .category-header {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
}

.category-bollywood .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

.category-hollywood {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid #667eea;
}

.category-hollywood .category-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-hollywood .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

.category-kdrama {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border: 2px solid #ff8a80;
}

.category-kdrama .category-header {
  background: linear-gradient(135deg, #ff8a80 0%, #ff5722 100%);
  color: white;
}

.category-kdrama .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

.category-anime {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border: 2px solid #4ecdc4;
}

.category-anime .category-header {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
}

.category-anime .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

.category-webseries {
  background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%);
  border: 2px solid #f093fb;
}

.category-webseries .category-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.category-webseries .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

.category-south {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: 2px solid #fdcb6e;
}

.category-south .category-header {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  color: white;
}

.category-south .category-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  background: none;
  -webkit-text-fill-color: white;
}

/* Category Animation Effects */
.category-component:hover .category-flag {
  animation: flagBounce 0.6s ease-in-out;
}

@keyframes flagBounce {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(-10deg);
  }
  50% {
    transform: scale(1.3) rotate(0deg);
  }
  75% {
    transform: scale(1.2) rotate(10deg);
  }
}

.category-component:hover .category-title {
  animation: titleGlow 1s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow: 0 0 5px var(--accent-primary);
  }
  100% {
    text-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
  }
}

/* Floating particles effect for categories */
.category-component::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 100px 100px, 150px 150px, 200px 200px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* Desktop Navigation Button Styles */
.desktop-nav a {
  position: relative;
  padding: 12px 20px !important;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  overflow: hidden;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid transparent;
}

.desktop-nav a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.desktop-nav a:hover::before {
  left: 100%;
}

.desktop-nav a:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: white !important;
  box-shadow: 0 8px 25px var(--shadow-medium);
  border-color: var(--accent-primary);
}

.desktop-nav a:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s;
}

/* Specific navbar button colors */
.desktop-nav a:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.desktop-nav a:nth-child(1):hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.desktop-nav a:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.desktop-nav a:nth-child(2):hover {
  background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.5);
}

.desktop-nav a:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.desktop-nav a:nth-child(3):hover {
  background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.5);
}

.desktop-nav a:nth-child(4) {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
}

.desktop-nav a:nth-child(4):hover {
  background: linear-gradient(135deg, #fee140 0%, #fa709a 100%);
  box-shadow: 0 8px 25px rgba(250, 112, 154, 0.5);
}

/* Navbar button pulse animation */
@keyframes navPulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
  }
}

.desktop-nav a:hover {
  animation: navPulse 2s ease-in-out infinite;
}