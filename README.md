# FilmFusion 🎬 
Check it out: https://filmfusion-live.netlify.app/

A modern movie and TV series discovery platform built with React and Node.js. Discover, rate, and manage your favorite movies and shows with a beautiful, responsive interface.

## Features

- 🎭 **Multi-Category Content**: Movies, TV shows, Bollywood, Hollywood, K-dramas, Web series, Anime, and South Indian content
- ⭐ **Rating System**: Rate and review your favorite content
- 📝 **Watchlist**: Save movies and shows to watch later
- 🔍 **Advanced Search**: Find content across all categories
- 🎨 **Theme Support**: Light and dark themes with custom color schemes
- 📱 **Responsive Design**: Optimized for desktop and mobile devices
- 🔐 **Authentication**: Secure login with Google OAuth integration
- 🚀 **Performance**: Redis caching and skeleton loading states

## Tech Stack

### Frontend
- **React 19** - Modern React with latest features
- **React Router** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations
- **Axios** - HTTP client for API calls

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **PostgreSQL** - Primary database
- **Redis** - Caching layer
- **JWT** - Authentication tokens
- **Google OAuth** - Social authentication

## Deployment

- **Frontend**: Deployed on Netlify
- **Backend**: Deployed on Railway
- **Database**: PostgreSQL on Railway
