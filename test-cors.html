<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>FilmFusion CORS Test</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';
            
            try {
                const response = await fetch('https://filmfusion-production.up.railway.app/api/health', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<p style="color: green;">✅ CORS Working! Response: ${JSON.stringify(data)}</p>`;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ HTTP Error: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">❌ CORS Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
