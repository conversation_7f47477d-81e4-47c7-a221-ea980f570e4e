{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@react-oauth/google": "^0.12.2", "@tailwindcss/postcss": "^4.1.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "framer-motion": "^12.15.0", "js-cookie": "^3.0.5", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "tailwindcss": "^4.1.8", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}