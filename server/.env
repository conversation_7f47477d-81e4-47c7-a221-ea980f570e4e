# Database Configuration - NeonDB
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
DATABASE_PUBLIC_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# JWT Secret
JWT_SECRET=your_strong_secret_key_for_filmfusion_2024

# TMDB API
TMDB_API_KEY=d0288711dbe8df028c10d016c4aca549
TMDB_BASE_URL=https://api.themoviedb.org/3

# Google OAuth
GOOGLE_CLIENT_ID=868268917843-tirbqp19qqt1a1vebp71icusqquhtjet.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Server Configuration
PORT=10000
NODE_ENV=production

# CORS Origins
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:5173,https://filmfusion-live.netlify.app
