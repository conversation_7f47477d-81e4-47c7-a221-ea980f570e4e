{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "npm cache clean --force && npm install --production --no-cache", "postinstall": "echo 'Dependencies installed successfully'", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "redis": "^5.1.1"}}